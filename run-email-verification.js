const AutoRegister = require('./index.js');
require('dotenv').config();

async function runEmailVerification() {
    const autoRegister = new AutoRegister();

    // 从环境变量、命令行参数或默认值获取URL
    const url = process.env.LINK_TO_TEST || process.argv[2] || 'https://www.augmentcode.com/';

    try {
        console.log('🚀 开始邮箱验证流程');
        console.log(`📧 目标URL: ${url}`);
        console.log('📝 使用One Mail API生成临时邮箱');
        console.log('⏰ 将自动获取验证码（2分钟超时，每5秒检查一次）');
        console.log('📸 每一步都会自动截图和保存HTML');
        console.log('');

        await autoRegister.handleEmailVerificationWithOneMailAPI(url);

        console.log('');
        console.log('🎉 邮箱验证流程完成！');
        console.log('📁 请查看 image/ 目录中的截图和HTML文件');

    } catch (error) {
        console.error('');
        console.error('💥 邮箱验证流程失败:', error.message);
        console.error('📁 请查看 image/ 目录中的错误截图和HTML文件进行调试');
        process.exit(1);
    }
}

if (require.main === module) {
    runEmailVerification();
}

{"name": "shell", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "test-onemail": "node test-onemail.js", "email-verification": "node run-email-verification.js", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "clipboardy": "^4.0.0", "imapflow": "^1.0.191", "mailparser": "^3.7.4", "nodemailer": "^7.0.5", "puppeteer": "^24.12.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "uuid": "^11.1.0"}}
const OneMailHandler = require('./onemail.js');

async function testOneMailAPI() {
    const oneMailHandler = new OneMailHandler();
    
    try {
        console.log('=== One Mail API 测试 ===');
        
        // 测试API连接
        console.log('\n1. 测试API连接...');
        const connected = await oneMailHandler.testConnection();
        if (!connected) {
            console.log('❌ API连接失败，请检查服务是否运行');
            return;
        }
        
        // 生成邮箱
        console.log('\n2. 生成临时邮箱...');
        const email = await oneMailHandler.generateEmail();
        console.log(`✅ 生成的邮箱: ${email}`);
        
        // 模拟等待验证码
        console.log('\n3. 模拟获取验证码...');
        console.log('请手动发送包含验证码的邮件到上述邮箱地址');
        console.log('然后等待程序自动获取验证码...');
        
        try {
            const code = await oneMailHandler.getVerificationCode(email, 1); // 1分钟超时用于测试
            console.log(`✅ 获取到验证码: ${code}`);
        } catch (error) {
            console.log(`⚠️ 获取验证码超时或失败: ${error.message}`);
            console.log('这是正常的，因为没有实际发送邮件');
        }
        
        console.log('\n✅ One Mail API 测试完成');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

if (require.main === module) {
    testOneMailAPI();
}

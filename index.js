const puppeteer = require('puppeteer');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const puppeteerExtra = require('puppeteer-extra');
const fs = require('fs');
const path = require('path');
const TokenHandler = require('./token.js');
const CaptchaHandler = require('./captcha.js');
const ProxyHandler = require('./proxy.js');
const EmailHandler = require('./email.js');
const OneMailHandler = require('./onemail.js');

puppeteerExtra.use(StealthPlugin());

class AutoRegister {
    constructor() {
        this.browser = null;
        this.page = null;
        this.authToken = null;
        this.baseUrl = 'http://127.0.0.1:3000（augment2api运行的ip加端口，nat机填本地IP';
        this.tempEmail = null;
        this.stepCounter = 0;
        this.currentProxy = null;
        this.networkResponses = [];

        this.tokenHandler = new TokenHandler();
        this.captchaHandler = new CaptchaHandler();
        this.proxyHandler = new ProxyHandler();
        this.emailHandler = new EmailHandler();
        this.oneMailHandler = new OneMailHandler();

        this.imageDir = path.join(__dirname, 'image');
        if (!fs.existsSync(this.imageDir)) {
            fs.mkdirSync(this.imageDir, { recursive: true });
        }
    }

    log(message) {
        console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
    }

    async takeScreenshot(stepName, isError = false) {
        try {
            this.stepCounter++;
            const prefix = isError ? 'ERROR' : 'STEP';
            const filename = `${prefix}_${this.stepCounter.toString().padStart(2, '0')}_${stepName}.png`;
            const filepath = path.join(this.imageDir, filename);
            await this.page.screenshot({ path: filepath, fullPage: true });
            this.log(`📸 ${filename}`);
        } catch (error) { }
    }

    async saveHtmlContent(stepName, isError = false) {
        try {
            const prefix = isError ? 'ERROR' : 'STEP';
            const filename = `${prefix}_${this.stepCounter.toString().padStart(2, '0')}_${stepName}.html`;
            const filepath = path.join(this.imageDir, filename);
            const htmlContent = await this.page.content();
            fs.writeFileSync(filepath, htmlContent, 'utf8');
            this.log(`💾 ${filename}`);
        } catch (error) {
            this.log(`保存HTML失败: ${error.message}`);
        }
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async saveDebugInfo(filename, data) {
        try {
            const debugDir = path.join(__dirname, 'debug');
            if (!fs.existsSync(debugDir)) {
                fs.mkdirSync(debugDir, { recursive: true });
            }

            const filepath = path.join(debugDir, filename);
            const jsonData = JSON.stringify(data, null, 2);
            fs.writeFileSync(filepath, jsonData, 'utf8');
            this.log(`💾 调试信息已保存: ${filename}`);
        } catch (error) {
            this.log(`保存调试信息失败: ${error.message}`);
        }
    }

    async detectCaptcha() {
        try {
            const captchaInfo = await this.page.evaluate(() => {
                const result = {
                    hasTurnstile: false,
                    hasRecaptcha: false,
                    siteKey: null,
                    currentUrl: window.location.href
                };

                const turnstileIframes = document.querySelectorAll('iframe[src*="challenges.cloudflare.com"]');
                if (turnstileIframes.length > 0) {
                    result.hasTurnstile = true;
                    const src = turnstileIframes[0].src;
                    const match = src.match(/0x4[A-Za-z0-9]{20,}/);
                    if (match) result.siteKey = match[0];
                }

                const siteKeyElements = document.querySelectorAll('[data-sitekey]');
                if (siteKeyElements.length > 0) {
                    const sitekey = siteKeyElements[0].getAttribute('data-sitekey');
                    if (sitekey && sitekey.startsWith('0x4')) {
                        result.hasTurnstile = true;
                        result.siteKey = sitekey;
                    }
                }

                if (window.grecaptcha || document.querySelector('.g-recaptcha') ||
                    document.querySelector('[data-sitekey*="6L"]')) {
                    result.hasRecaptcha = true;
                }

                if (!result.siteKey) {
                    const pageContent = document.documentElement.outerHTML;
                    const match = pageContent.match(/0x4[A-Za-z0-9]{20,}/);
                    if (match) {
                        result.siteKey = match[0];
                        result.hasTurnstile = true;
                    }
                }

                return result;
            });

            return captchaInfo;
        } catch (error) {
            return { hasTurnstile: false, hasRecaptcha: false, siteKey: null, currentUrl: this.page.url() };
        }
    }

    async handleCaptchaIfPresent() {
        const captchaInfo = await this.detectCaptcha();

        if (!captchaInfo.hasTurnstile && !captchaInfo.hasRecaptcha) {
            this.log('未检测到验证码');
            return true;
        }

        this.log('检测到验证码');
        await this.takeScreenshot('captcha_detected');

        // 调试模式：使用YesCaptcha获取token并保存调试信息
        this.log('🔍 开始调试验证码状态...');

        // 先检查初始状态
        const initialStatus = await this.checkCaptchaStatus();
        this.log(`初始验证码状态: ${JSON.stringify(initialStatus, null, 2)}`);

        // 保存初始状态到文件
        await this.saveDebugInfo('initial_captcha_status.json', initialStatus);

        let success = false;
        let debugData = {
            initialStatus,
            yescaptchaUsed: false,
            token: null,
            injectionResult: null,
            finalStatus: null,
            screenshots: [],
            timestamp: new Date().toISOString()
        };

        if (captchaInfo.hasTurnstile && captchaInfo.siteKey) {
            this.log('🎯 使用YesCaptcha处理Turnstile验证码...');
            debugData.yescaptchaUsed = true;

            success = await this.captchaHandler.handleTurnstile(this.page, captchaInfo.currentUrl, captchaInfo.siteKey);

            if (success) {
                this.log('✅ YesCaptcha返回成功');
                await this.takeScreenshot('yescaptcha_success');
                debugData.screenshots.push('yescaptcha_success.png');

                // 等待一段时间让页面处理token
                await this.wait(5000);

                // 检查token注入后的状态
                const afterTokenStatus = await this.checkCaptchaStatus();
                this.log(`Token注入后状态: ${JSON.stringify(afterTokenStatus, null, 2)}`);
                debugData.finalStatus = afterTokenStatus;

                await this.takeScreenshot('after_token_injection');
                debugData.screenshots.push('after_token_injection.png');

            } else {
                this.log('❌ YesCaptcha处理失败');
                await this.takeScreenshot('yescaptcha_failed', true);
                debugData.screenshots.push('yescaptcha_failed.png');
            }
        }

        if (captchaInfo.hasRecaptcha) {
            this.log('🎯 处理reCAPTCHA...');
            const recaptchaSuccess = await this.captchaHandler.handleRecaptchaEnterprise(this.page);
            success = success || recaptchaSuccess;
        }

        // 最终状态检查
        const finalStatus = await this.checkCaptchaStatus();
        debugData.finalStatus = finalStatus;
        this.log(`最终验证码状态: ${JSON.stringify(finalStatus, null, 2)}`);

        // 保存完整的调试数据
        await this.saveDebugInfo('complete_captcha_debug.json', debugData);

        if (success && finalStatus.isResolved) {
            this.log('✅ 验证码处理成功');
            await this.takeScreenshot('captcha_solved');
            return true;
        } else {
            this.log('❌ 验证码处理失败');
            await this.takeScreenshot('captcha_failed', true);
            return false;
        }
    }

    async checkCaptchaStatus() {
        try {
            const status = await this.page.evaluate(() => {
                const result = {
                    isResolved: false,
                    turnstileStatus: null,
                    recaptchaStatus: null,
                    visibleElements: [],
                    hiddenInputs: []
                };

                // 检查所有iframe和验证码相关元素
                const allIframes = document.querySelectorAll('iframe');
                const turnstileIframes = document.querySelectorAll('iframe[src*="challenges.cloudflare.com"]');
                const turnstileWidgets = document.querySelectorAll('[data-sitekey]');
                const cfTurnstile = document.querySelectorAll('.cf-turnstile');

                // 记录所有iframe
                allIframes.forEach((iframe, index) => {
                    const rect = iframe.getBoundingClientRect();
                    result.visibleElements.push({
                        type: 'iframe',
                        index,
                        visible: rect.width > 0 && rect.height > 0,
                        src: iframe.src || 'no-src',
                        dimensions: { width: rect.width, height: rect.height }
                    });
                });

                // 记录Turnstile相关元素
                turnstileWidgets.forEach((widget, index) => {
                    const rect = widget.getBoundingClientRect();
                    result.visibleElements.push({
                        type: 'turnstile-widget',
                        index,
                        visible: rect.width > 0 && rect.height > 0,
                        sitekey: widget.getAttribute('data-sitekey'),
                        dimensions: { width: rect.width, height: rect.height }
                    });
                });

                cfTurnstile.forEach((element, index) => {
                    const rect = element.getBoundingClientRect();
                    result.visibleElements.push({
                        type: 'cf-turnstile-element',
                        index,
                        visible: rect.width > 0 && rect.height > 0,
                        dimensions: { width: rect.width, height: rect.height }
                    });
                });

                // 检查Turnstile响应字段
                const turnstileInputs = document.querySelectorAll('input[name="cf-turnstile-response"]');
                turnstileInputs.forEach((input, index) => {
                    result.hiddenInputs.push({
                        type: 'turnstile-response',
                        index,
                        hasValue: input.value && input.value.length > 0,
                        valueLength: input.value ? input.value.length : 0
                    });

                    if (input.value && input.value.length > 0) {
                        result.isResolved = true;
                        result.turnstileStatus = 'resolved';
                    }
                });

                // 检查reCAPTCHA状态
                const recaptchaInputs = document.querySelectorAll('input[name="g-recaptcha-response"]');
                recaptchaInputs.forEach((input, index) => {
                    result.hiddenInputs.push({
                        type: 'recaptcha-response',
                        index,
                        hasValue: input.value && input.value.length > 0,
                        valueLength: input.value ? input.value.length : 0
                    });

                    if (input.value && input.value.length > 0) {
                        result.isResolved = true;
                        result.recaptchaStatus = 'resolved';
                    }
                });

                // 检查Cloudflare clearance cookie (更可靠的验证码通过指示)
                const cookies = document.cookie;
                if (cookies.includes('cf_clearance=')) {
                    result.isResolved = true;
                    result.turnstileStatus = 'cleared_by_cookie';
                }

                // 检查是否还有可见的验证码挑战
                const visibleChallenges = result.visibleElements.filter(el => el.visible);
                if (visibleChallenges.length > 0) {
                    // 如果还有可见的验证码挑战，则认为未解决
                    result.isResolved = false;
                }

                return result;
            });

            return status;
        } catch (error) {
            this.log(`检查验证码状态失败: ${error.message}`);
            return { isResolved: false, error: error.message };
        }
    }

    async tryClickCaptcha() {
        try {
            const clicked = await this.page.evaluate(() => {
                const clickableElements = [];

                // 收集所有可能的验证码元素
                const selectors = [
                    'iframe',
                    '[data-sitekey]',
                    '.cf-turnstile',
                    '[id*="turnstile"]',
                    '[class*="turnstile"]',
                    '.g-recaptcha',
                    '[id*="recaptcha"]',
                    '[class*="captcha"]',
                    '[id*="captcha"]'
                ];

                selectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => {
                        const rect = el.getBoundingClientRect();
                        if (rect.width > 0 && rect.height > 0) {
                            clickableElements.push({
                                element: el,
                                selector: selector,
                                rect: rect,
                                src: el.src || 'no-src',
                                id: el.id || 'no-id',
                                className: el.className || 'no-class'
                            });
                        }
                    });
                });

                console.log('Found clickable elements:', clickableElements.length);
                clickableElements.forEach((item, index) => {
                    console.log(`Element ${index}:`, {
                        selector: item.selector,
                        id: item.id,
                        className: item.className,
                        src: item.src,
                        dimensions: `${item.rect.width}x${item.rect.height}`
                    });
                });

                // 尝试点击第一个可见元素
                if (clickableElements.length > 0) {
                    try {
                        clickableElements[0].element.click();
                        console.log('Clicked element:', clickableElements[0].selector);
                        return true;
                    } catch (e) {
                        console.log('Click failed:', e.message);
                    }
                }

                return false;
            });

            return clicked;
        } catch (error) {
            this.log(`点击验证码失败: ${error.message}`);
            return false;
        }
    }

    async initBrowser() {
        try {
            // this.currentProxy = await this.proxyHandler.getValidProxy();
            // this.log(`代理: ${this.currentProxy}`);
        } catch (error) {
            this.log('无代理运行');
            this.currentProxy = null;
        }

        const launchOptions = {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--headless=new',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',
                '--ignore-certificate-errors',
                '--disable-blink-features=AutomationControlled'
            ],
            defaultViewport: { width: 1366, height: 768 },
            timeout: 120000
        };

        // if (this.currentProxy) {
        //     launchOptions.args.push(`--proxy-server=http://${this.currentProxy}`);
        // }

        this.browser = await puppeteerExtra.launch(launchOptions);
        this.page = await this.browser.newPage();
        this.page.setDefaultTimeout(120000);
        this.page.setDefaultNavigationTimeout(120000);

        // 设置网络响应监听
        this.networkResponses = [];
        await this.page.setRequestInterception(true);

        this.page.on('request', (request) => {
            request.continue();
        });

        this.page.on('response', async (response) => {
            try {
                const url = response.url();
                const status = response.status();
                const headers = response.headers();

                // 只记录可能包含认证信息的响应
                if (url.includes('auth') || url.includes('login') || url.includes('token') ||
                    url.includes('api') || status === 200) {

                    let responseBody = null;
                    try {
                        const contentType = headers['content-type'] || '';
                        if (contentType.includes('application/json') || contentType.includes('text/')) {
                            responseBody = await response.text();
                        }
                    } catch (e) {
                        // 忽略无法读取的响应体
                    }

                    this.networkResponses.push({
                        url,
                        status,
                        headers,
                        body: responseBody,
                        timestamp: new Date().toISOString()
                    });
                }
            } catch (error) {
                // 忽略响应处理错误
            }
        });

        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        await this.page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            window.chrome = { runtime: {} };
            Object.defineProperty(navigator, 'languages', { get: () => ['en-US', 'en'] });
            Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5] });
        });

        this.log('浏览器启动');
        return this.page;
    }

    async findInputField(selectors) {
        for (const selector of selectors) {
            try {
                await this.page.waitForSelector(selector, { timeout: 5000 });
                const input = await this.page.$(selector);
                if (input) return input;
            } catch (e) { continue; }
        }
        return null;
    }

    async clickButtonContaining(keywords, timeout = 15000) {
        try {
            await this.page.waitForSelector('button, a, input[type="submit"], [role="button"]', { timeout });
            const elements = await this.page.$$('button, a, input[type="submit"], [role="button"]');

            for (const element of elements) {
                const text = await this.page.evaluate(el => {
                    return (el.textContent || el.value || el.getAttribute('aria-label') || '').trim();
                }, element);

                for (const keyword of keywords) {
                    if (text.toLowerCase().includes(keyword.toLowerCase())) {
                        await element.click();
                        await this.wait(1000);
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    async register() {
        try {
            await this.initBrowser();
            await this.takeScreenshot('browser_started');

            //
            this.authToken = await this.tokenHandler.getAuthToken(this.page, this.baseUrl);
            await this.takeScreenshot('auth_token_obtained');

            // 生成临时邮箱
            this.tempEmail = await this.emailHandler.generateTempEmail();
            this.log(`邮箱: ${this.tempEmail}`);

            const authUrl = await this.tokenHandler.getAuthUrl(this.baseUrl, this.authToken);
            this.log(`访问页面B: ${authUrl}`);
            await this.page.goto(authUrl, { waitUntil: 'domcontentloaded', timeout: 120000 });
            await this.wait(5000);
            await this.takeScreenshot('page_B_loaded');

            await this.handleCaptchaIfPresent();

            this.log('填入邮箱');
            const emailSelectors = ['input[type="email"]', 'input[name="email"]', 'input[name="username"]', 'input[id="username"]'];
            const emailInput = await this.findInputField(emailSelectors);
            if (!emailInput) {
                await this.takeScreenshot('no_email_input_B', true);
                throw new Error('页面B未找到邮箱输入框');
            }

            await emailInput.click();
            await this.wait(500);
            await emailInput.type(this.tempEmail, { delay: 100 });
            await this.takeScreenshot('email_entered_B');

            this.log('提交邮箱');
            const emailSubmitted = await this.clickButtonContaining(['Continue', 'Next', '继续']);
            if (!emailSubmitted) await this.page.keyboard.press('Enter');

            await this.wait(8000);
            await this.takeScreenshot('page_C_loaded');
            this.log(`页面C: ${this.page.url()}`);

            await this.handleCaptchaIfPresent();

            // 等待验证码 - email.js内部会自动处理邮件列表检查和验证码提取
            this.log('等待验证码...');
            const verificationCode = await this.emailHandler.waitForVerificationCode();

            this.log('填入验证码');
            const codeSelectors = ['input[name="code"]', 'input[placeholder*="code" i]', 'input[type="text"]:not([readonly])', 'input[autocomplete="one-time-code"]'];
            const codeInput = await this.findInputField(codeSelectors);
            if (!codeInput) {
                await this.takeScreenshot('no_code_input_C', true);
                throw new Error('页面C未找到验证码输入框');
            }

            await codeInput.click();
            await this.wait(500);
            await codeInput.type(verificationCode, { delay: 100 });
            await this.takeScreenshot('code_entered_C');

            await this.handleCaptchaIfPresent();

            this.log('提交验证码');
            const codeSubmitted = await this.clickButtonContaining(['Continue', 'Next', '继续', 'Verify']);
            if (!codeSubmitted) await this.page.keyboard.press('Enter');
            await this.wait(10000);
            await this.takeScreenshot('code_submitted_C');

            const pageContent = await this.page.content();
            if (pageContent.includes('error') || pageContent.includes('failed') || pageContent.includes('blocked')) {
                await this.takeScreenshot('registration_blocked', true);
                await this.handleCaptchaIfPresent();
                await this.takeScreenshot('retry_captcha');
            }

            try {
                const checkboxes = await this.page.$$('input[type="checkbox"], [role="checkbox"]');
                for (const checkbox of checkboxes) {
                    const isChecked = await this.page.evaluate(el =>
                        el.checked || el.getAttribute('aria-checked') === 'true', checkbox);
                    if (!isChecked) {
                        await checkbox.click();
                        await this.takeScreenshot('checkbox_checked');
                        break;
                    }
                }
            } catch (e) { }

            this.log('点击注册');
            const signupClicked = await this.clickButtonContaining(['sign up', 'register', '注册', 'create account']);
            if (!signupClicked) {
                await this.takeScreenshot('no_signup_button', true);
                throw new Error('未找到注册按钮');
            }
            await this.wait(15000);
            await this.takeScreenshot('signup_clicked');

            const jsonData = await this.getJsonData();
            if (jsonData) {
                // await this.tokenHandler.submitCallback(this.baseUrl, this.authToken, jsonData);
                console.log('成功获取到注册数据:');
                console.log(JSON.stringify(jsonData, null, 2));
                await this.takeScreenshot('data_logged');
            }

            await this.cleanup();

            const result = {
                email: this.tempEmail,
                proxy: this.currentProxy,
                status: 'success',
                timestamp: new Date().toISOString()
            };

            this.log(`注册完成: ${this.tempEmail}`);
            return result;

        } catch (error) {
            await this.takeScreenshot('registration_error', true);
            this.log(`注册失败: ${error.message}`);
            await this.cleanup();
            throw error;
        }
    }

    async getJsonData() {
        try {
            await this.page.evaluate(() => {
                window.clipboardData = '';
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    const originalWriteText = navigator.clipboard.writeText;
                    navigator.clipboard.writeText = function (text) {
                        window.clipboardData = text;
                        return originalWriteText.call(this, text);
                    };
                }
            });

            const copyClicked = await this.clickButtonContaining(['copy', 'cop', '复制']);
            if (copyClicked) {
                await this.wait(2000);
                const clipboardData = await this.page.evaluate(() => window.clipboardData);
                if (clipboardData) {
                    return JSON.parse(clipboardData);
                }
            }

            const jsonData = await this.page.evaluate(() => {
                const scripts = Array.from(document.getElementsByTagName('script'));
                for (const script of scripts) {
                    if (script.textContent.includes('let data = {')) {
                        const match = script.textContent.match(/let data = ({[\s\S]*?});/);
                        if (match && match[1]) {
                            return JSON.parse(match[1]);
                        }
                    }
                }
                return null;
            });

            return jsonData;
        } catch (error) {
            return null;
        }
    }

    async cleanup() {
        if (this.emailHandler) this.emailHandler.clearCurrentEmail();
        if (this.oneMailHandler) this.oneMailHandler.clearCurrentEmail();
        if (this.proxyHandler) this.proxyHandler.clearProxy();
        if (this.browser) {
            try {
                await this.browser.close();
            } catch (error) { }
            this.browser = null;
            this.page = null;
        }
    }

    generateRandomEmail() {
        const randomString = Math.random().toString(36).substring(2, 10);
        return `abc+${randomString}@techexpresser.com`;
    }

    async captureNetworkResponses() {
        try {
            this.log('Capturing network responses and tokens...');

            // 等待一下确保所有请求完成
            await this.wait(2000);

            // 尝试从页面中获取所有可能的token和response数据
            const pageData = await this.page.evaluate(() => {
                const result = {
                    url: window.location.href,
                    localStorage: {},
                    sessionStorage: {},
                    cookies: document.cookie,
                    pageContent: document.documentElement.outerHTML,
                    tokens: []
                };

                // 获取localStorage
                try {
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        result.localStorage[key] = localStorage.getItem(key);
                    }
                } catch (e) { }

                // 获取sessionStorage
                try {
                    for (let i = 0; i < sessionStorage.length; i++) {
                        const key = sessionStorage.key(i);
                        result.sessionStorage[key] = sessionStorage.getItem(key);
                    }
                } catch (e) { }

                // 查找页面中的token
                const tokenPatterns = [
                    /token["\s]*[:=]["\s]*([a-zA-Z0-9._-]+)/gi,
                    /access_token["\s]*[:=]["\s]*([a-zA-Z0-9._-]+)/gi,
                    /auth_token["\s]*[:=]["\s]*([a-zA-Z0-9._-]+)/gi,
                    /bearer["\s]*[:=]["\s]*([a-zA-Z0-9._-]+)/gi,
                    /jwt["\s]*[:=]["\s]*([a-zA-Z0-9._-]+)/gi
                ];

                tokenPatterns.forEach(pattern => {
                    let match;
                    while ((match = pattern.exec(result.pageContent)) !== null) {
                        result.tokens.push({
                            type: pattern.source.split('[')[0],
                            value: match[1]
                        });
                    }
                });

                return result;
            });

            // 输出完整的response数据
            console.log('\n=== COMPLETE RESPONSE DATA ===');
            console.log('Current URL:', pageData.url);
            console.log('\n--- Cookies ---');
            console.log(pageData.cookies);
            console.log('\n--- Local Storage ---');
            console.log(JSON.stringify(pageData.localStorage, null, 2));
            console.log('\n--- Session Storage ---');
            console.log(JSON.stringify(pageData.sessionStorage, null, 2));
            console.log('\n--- Found Tokens ---');
            console.log(JSON.stringify(pageData.tokens, null, 2));

            // 尝试获取网络请求的响应
            const responses = await this.page.evaluate(() => {
                // 如果页面有全局的响应数据，尝试获取
                const globalVars = [];
                for (const key in window) {
                    if (typeof window[key] === 'object' && window[key] !== null) {
                        try {
                            const str = JSON.stringify(window[key]);
                            if (str.includes('token') || str.includes('auth') || str.includes('access')) {
                                globalVars.push({
                                    key: key,
                                    value: window[key]
                                });
                            }
                        } catch (e) { }
                    }
                }
                return globalVars;
            });

            if (responses.length > 0) {
                console.log('\n--- Global Variables with Auth Data ---');
                console.log(JSON.stringify(responses, null, 2));
            }

            // 输出网络响应数据
            if (this.networkResponses && this.networkResponses.length > 0) {
                console.log('\n--- Network Responses ---');
                this.networkResponses.forEach((response, index) => {
                    console.log(`\nResponse ${index + 1}:`);
                    console.log(`URL: ${response.url}`);
                    console.log(`Status: ${response.status}`);
                    console.log(`Headers:`, JSON.stringify(response.headers, null, 2));
                    if (response.body) {
                        console.log(`Body:`, response.body);
                    }
                    console.log(`Timestamp: ${response.timestamp}`);
                });
            }

            console.log('\n=== END RESPONSE DATA ===\n');

        } catch (error) {
            this.log(`Error capturing responses: ${error.message}`);
        }
    }

    async signInAndSolveCaptcha(url) {
        try {
            await this.initBrowser();
            await this.takeScreenshot('browser_started');

            this.log(`Navigating to ${url}`);
            await this.page.goto(url, { waitUntil: 'networkidle2', timeout: 120000 });
            await this.takeScreenshot('page_loaded');

            this.log('Looking for "Sign In" button');
            const signInClicked = await this.clickButtonContaining(['sign in']);
            if (signInClicked) {
                this.log('Clicked "Sign In" button.');
                await this.wait(5000); // Wait for page transition
                await this.takeScreenshot('sign_in_clicked');
            } else {
                this.log('Could not find "Sign In" button.');
                await this.takeScreenshot('no_sign_in_button', true);
            }

            this.log('Checking for CAPTCHA...');
            const captchaSolved = await this.handleCaptchaIfPresent();

            if (captchaSolved) {
                this.log('CAPTCHA handled successfully.');

                // 步骤1: 截图
                await this.takeScreenshot('after_captcha_solved');

                // 步骤2: 输入邮箱地址
                const randomEmail = this.generateRandomEmail();
                this.log(`Generated email: ${randomEmail}`);
                console.log(`\n🔥 GENERATED EMAIL: ${randomEmail} 🔥\n`);

                const emailSelectors = [
                    'input[type="email"]',
                    'input[name="email"]',
                    'input[name="username"]',
                    'input[id="username"]',
                    'input[placeholder*="email" i]',
                    'input[placeholder*="Email" i]'
                ];

                const emailInput = await this.findInputField(emailSelectors);
                if (emailInput) {
                    await emailInput.click();
                    await this.wait(500);
                    await emailInput.type(randomEmail, { delay: 100 });
                    this.log('Email address entered successfully.');

                    // 步骤3: 截图
                    await this.takeScreenshot('email_entered');

                    // 步骤4: 点击continue按钮
                    this.log('Looking for Continue button...');
                    const continueClicked = await this.clickButtonContaining(['Continue', 'Next', '继续', 'Submit']);
                    if (continueClicked) {
                        this.log('Continue button clicked successfully.');
                        await this.wait(3000); // Wait for response

                        // 步骤5: 截图
                        await this.takeScreenshot('continue_clicked');

                        // 步骤6: 获取并输出完整的response
                        await this.captureNetworkResponses();

                    } else {
                        this.log('Could not find Continue button, trying Enter key...');
                        await this.page.keyboard.press('Enter');
                        await this.wait(3000);
                        await this.takeScreenshot('enter_pressed');
                        await this.captureNetworkResponses();
                    }
                } else {
                    this.log('Could not find email input field.');
                    await this.takeScreenshot('no_email_input', true);
                }

            } else {
                this.log('No CAPTCHA found or failed to handle.');
            }

            this.log('Process finished.');

        } catch (error) {
            await this.takeScreenshot('process_error', true);
            this.log(`An error occurred: ${error.message}`);
            throw error;
        } finally {
            await this.cleanup();
        }
    }

    // 新方法：处理邮箱验证码流程
    async handleEmailVerificationWithOneMailAPI(url) {
        try {
            await this.initBrowser();

            // 设置网络监听以捕获授权相关的请求和响应
            await this.setupNetworkMonitoring();

            await this.takeScreenshot('browser_started');
            await this.saveHtmlContent('browser_started');

            // 测试One Mail API连接
            const apiConnected = await this.oneMailHandler.testConnection();
            if (!apiConnected) {
                throw new Error('One Mail API连接失败');
            }

            this.log(`导航到页面: ${url}`);
            await this.page.goto(url, { waitUntil: 'networkidle2', timeout: 120000 });
            await this.takeScreenshot('page_loaded');
            await this.saveHtmlContent('page_loaded');

            // 检查是否直接到了登录页面（通过URL判断）
            const currentUrl = this.page.url();
            if (currentUrl.includes('login') || currentUrl.includes('auth')) {
                this.log('✅ 已直接进入登录页面，跳过Sign In步骤');
            } else {
                // 如果不是登录页面，尝试点击 Sign In 按钮
                this.log('查找并点击 Sign In 按钮...');
                const signInClicked = await this.clickButtonContaining(['sign in', 'login', '登录', '登入']);
                if (signInClicked) {
                    this.log('✅ 点击了 Sign In 按钮');
                    await this.wait(5000); // 等待页面跳转
                    await this.takeScreenshot('sign_in_clicked');
                    await this.saveHtmlContent('sign_in_clicked');
                } else {
                    this.log('❌ 未找到 Sign In 按钮');
                    await this.takeScreenshot('no_sign_in_button', true);
                    await this.saveHtmlContent('no_sign_in_button', true);
                    throw new Error('未找到 Sign In 按钮');
                }
            }

            // 处理可能的验证码
            await this.handleCaptchaIfPresent();

            // 现在生成临时邮箱
            const tempEmail = await this.oneMailHandler.generateEmail();
            this.log(`生成的邮箱: ${tempEmail}`);

            // 查找邮箱输入框并填入邮箱
            const emailInputSelectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[name="username"]',
                'input[id="username"]',
                'input[inputmode="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="Email"]',
                'input[id*="email"]',
                'input[class*="email"]'
            ];

            const emailInput = await this.findInputField(emailInputSelectors);
            if (emailInput) {
                await emailInput.click();
                await this.wait(1000);
                await emailInput.type(tempEmail, { delay: 100 });
                this.log(`邮箱已填入: ${tempEmail}`);
                await this.takeScreenshot('email_entered');
                await this.saveHtmlContent('email_entered');

                // 点击Continue按钮
                const continueClicked = await this.clickButtonContaining(['continue', 'next', '继续', '下一步']);
                if (continueClicked) {
                    this.log('点击了Continue按钮');
                    await this.wait(5000);
                    await this.takeScreenshot('continue_clicked');
                    await this.saveHtmlContent('continue_clicked');
                } else {
                    this.log('未找到Continue按钮，尝试按Enter键');
                    await this.page.keyboard.press('Enter');
                    await this.wait(5000);
                    await this.takeScreenshot('enter_pressed');
                    await this.saveHtmlContent('enter_pressed');
                }

                // 等待验证码输入页面加载
                await this.wait(3000);
                await this.takeScreenshot('verification_page_loaded');
                await this.saveHtmlContent('verification_page_loaded');

                // 开始获取验证码（2分钟超时，每5秒检查一次）
                this.log('开始获取验证码...');
                const verificationCode = await this.oneMailHandler.getVerificationCode(tempEmail, 2);
                this.log(`获取到验证码: ${verificationCode}`);

                // 查找验证码输入框
                const codeInputSelectors = [
                    'input[type="text"]',
                    'input[name="code"]',
                    'input[name="verification_code"]',
                    'input[name="verificationCode"]',
                    'input[placeholder*="code"]',
                    'input[placeholder*="Code"]',
                    'input[placeholder*="验证码"]',
                    'input[id*="code"]',
                    'input[class*="code"]'
                ];

                const codeInput = await this.findInputField(codeInputSelectors);
                if (codeInput) {
                    await codeInput.click();
                    await this.wait(1000);
                    await codeInput.type(verificationCode, { delay: 100 });
                    this.log(`验证码已填入: ${verificationCode}`);
                    await this.takeScreenshot('code_entered');
                    await this.saveHtmlContent('code_entered');

                    // 再次点击Continue按钮
                    const finalContinueClicked = await this.clickButtonContaining(['continue', 'verify', 'submit', '继续', '验证', '提交']);
                    if (finalContinueClicked) {
                        this.log('点击了最终的Continue按钮');
                        await this.wait(5000);
                        await this.takeScreenshot('final_continue_clicked');
                        await this.saveHtmlContent('final_continue_clicked');
                    } else {
                        this.log('未找到最终Continue按钮，尝试按Enter键');
                        await this.page.keyboard.press('Enter');
                        await this.wait(5000);
                        await this.takeScreenshot('final_enter_pressed');
                        await this.saveHtmlContent('final_enter_pressed');
                    }

                    // 等待页面响应和可能的重定向
                    await this.wait(3000);
                    await this.takeScreenshot('verification_complete');
                    await this.saveHtmlContent('verification_complete');

                    // 点击 copy to clipboard 按钮并获取内容
                    await this.clickCopyToClipboardAndGetContent();

                    // 监听授权响应
                    await this.captureAuthorizationResponse();

                    this.log('✅ 邮箱验证流程完成');

                } else {
                    this.log('❌ 未找到验证码输入框');
                    await this.takeScreenshot('no_code_input', true);
                    await this.saveHtmlContent('no_code_input', true);
                }

            } else {
                this.log('❌ 未找到邮箱输入框');
                await this.takeScreenshot('no_email_input', true);
                await this.saveHtmlContent('no_email_input', true);
            }

        } catch (error) {
            this.log(`❌ 邮箱验证流程出错: ${error.message}`);
            await this.takeScreenshot('verification_error', true);
            await this.saveHtmlContent('verification_error', true);
            throw error;
        } finally {
            await this.cleanup();
        }
    }

    // 点击 copy to clipboard 按钮并获取内容
    async clickCopyToClipboardAndGetContent() {
        try {
            this.log('🔍 查找 copy to clipboard 按钮...');

            // 设置剪贴板拦截
            await this.page.evaluate(() => {
                window.clipboardData = '';

                // 拦截 navigator.clipboard.writeText
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    const originalWriteText = navigator.clipboard.writeText;
                    navigator.clipboard.writeText = function(text) {
                        window.clipboardData = text;
                        console.log('剪贴板内容已拦截:', text);
                        return originalWriteText.call(this, text);
                    };
                }

                // 拦截 document.execCommand('copy')
                const originalExecCommand = document.execCommand;
                document.execCommand = function(command, ...args) {
                    if (command === 'copy') {
                        const selection = window.getSelection();
                        if (selection.rangeCount > 0) {
                            window.clipboardData = selection.toString();
                            console.log('通过execCommand复制的内容:', window.clipboardData);
                        }
                    }
                    return originalExecCommand.call(this, command, ...args);
                };
            });

            // 查找copy按钮的多种可能选择器
            const copyButtonSelectors = [
                'button[title*="copy"]',
                'button[aria-label*="copy"]',
                'button:contains("copy")',
                'button:contains("Copy")',
                'button:contains("COPY")',
                '[role="button"]:contains("copy")',
                '[role="button"]:contains("Copy")',
                '.copy-button',
                '.btn-copy',
                'button[class*="copy"]',
                'span:contains("copy to clipboard")',
                'span:contains("Copy to clipboard")',
                'div:contains("copy to clipboard")',
                'div:contains("Copy to clipboard")'
            ];

            let copyButton = null;

            // 尝试通过文本内容查找按钮
            copyButton = await this.page.evaluateHandle(() => {
                const buttons = Array.from(document.querySelectorAll('button, [role="button"], span, div'));
                return buttons.find(btn => {
                    const text = btn.textContent || btn.innerText || '';
                    return text.toLowerCase().includes('copy') &&
                           (text.toLowerCase().includes('clipboard') || text.toLowerCase().includes('copy'));
                });
            });

            if (!copyButton || !copyButton.asElement()) {
                // 如果没找到，尝试其他选择器
                for (const selector of copyButtonSelectors) {
                    try {
                        await this.page.waitForSelector(selector, { timeout: 2000 });
                        copyButton = await this.page.$(selector);
                        if (copyButton) {
                            this.log(`✅ 找到copy按钮: ${selector}`);
                            break;
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }

            if (copyButton && copyButton.asElement()) {
                this.log('🖱️ 点击 copy to clipboard 按钮...');
                await copyButton.click();
                await this.wait(2000); // 等待复制操作完成

                // 获取拦截到的剪贴板内容
                const clipboardContent = await this.page.evaluate(() => window.clipboardData);

                if (clipboardContent) {
                    console.log('\n📋 ===== 复制到剪贴板的内容 =====');
                    console.log(clipboardContent);
                    console.log('================================\n');

                    // 保存到文件
                    const clipboardFile = path.join(this.imageDir, 'clipboard_content.txt');
                    fs.writeFileSync(clipboardFile, clipboardContent, 'utf8');
                    this.log(`💾 剪贴板内容已保存到: clipboard_content.txt`);

                    // 尝试解析为JSON
                    try {
                        const jsonData = JSON.parse(clipboardContent);
                        console.log('\n🔐 ===== 解析的JSON数据 =====');
                        console.log(JSON.stringify(jsonData, null, 2));
                        console.log('============================\n');

                        const jsonFile = path.join(this.imageDir, 'clipboard_content.json');
                        fs.writeFileSync(jsonFile, JSON.stringify(jsonData, null, 2), 'utf8');
                        this.log(`💾 JSON数据已保存到: clipboard_content.json`);
                    } catch (e) {
                        this.log('剪贴板内容不是有效的JSON格式');
                    }

                    await this.takeScreenshot('copy_button_clicked');
                    await this.saveHtmlContent('copy_button_clicked');

                    return clipboardContent;
                } else {
                    this.log('⚠️ 未获取到剪贴板内容');
                    await this.takeScreenshot('copy_button_clicked_no_content');
                    return null;
                }
            } else {
                this.log('❌ 未找到 copy to clipboard 按钮');
                await this.takeScreenshot('no_copy_button', true);

                // 尝试查找页面中的所有可能包含数据的元素
                const pageData = await this.page.evaluate(() => {
                    const result = {};

                    // 查找包含JSON的元素
                    const elements = document.querySelectorAll('pre, code, textarea, div[class*="json"], div[class*="code"]');
                    elements.forEach((el, index) => {
                        const text = el.textContent || el.innerText || '';
                        if (text.trim() && (text.includes('{') || text.includes('code') || text.includes('token'))) {
                            result[`element_${index}`] = text.trim();
                        }
                    });

                    return result;
                });

                if (Object.keys(pageData).length > 0) {
                    console.log('\n📄 ===== 页面中找到的可能数据 =====');
                    console.log(JSON.stringify(pageData, null, 2));
                    console.log('=================================\n');

                    const pageDataFile = path.join(this.imageDir, 'page_data_fallback.json');
                    fs.writeFileSync(pageDataFile, JSON.stringify(pageData, null, 2), 'utf8');
                    this.log(`💾 页面数据已保存到: page_data_fallback.json`);
                }

                return null;
            }

        } catch (error) {
            this.log(`❌ 点击copy按钮时出错: ${error.message}`);
            await this.takeScreenshot('copy_button_error', true);
            return null;
        }
    }

    // 设置网络监听
    async setupNetworkMonitoring() {
        try {
            this.log('🌐 设置网络监听...');

            // 监听所有网络请求
            await this.page.setRequestInterception(true);

            this.page.on('request', (request) => {
                const url = request.url();

                // 记录授权相关的请求
                if (url.includes('authorize') || url.includes('token') || url.includes('oauth') ||
                    url.includes('auth') || url.includes('login') || url.includes('callback')) {
                    this.log(`📤 授权相关请求: ${request.method()} ${url}`);
                }

                // 检查请求是否已经被处理
                if (!request.isInterceptResolutionHandled()) {
                    request.continue();
                }
            });

            this.page.on('response', async (response) => {
                const url = response.url();
                const status = response.status();

                // 记录授权相关的响应
                if (url.includes('authorize') || url.includes('token') || url.includes('oauth') ||
                    url.includes('auth') || url.includes('login') || url.includes('callback')) {
                    this.log(`📥 授权相关响应: ${status} ${url}`);

                    // 如果是重定向响应，记录Location头
                    if (status >= 300 && status < 400) {
                        const location = response.headers()['location'];
                        if (location) {
                            this.log(`🔄 重定向到: ${location}`);

                            // 检查重定向URL是否包含授权码
                            if (location.includes('code=') || location.includes('access_token=')) {
                                console.log('\n🎉 ===== 在重定向中检测到授权响应 =====');
                                console.log('📍 重定向URL:', location);

                                // 解析重定向URL中的参数
                                try {
                                    const redirectUrl = new URL(location);
                                    const authParams = {};

                                    ['code', 'access_token', 'state', 'error', 'error_description'].forEach(param => {
                                        const value = redirectUrl.searchParams.get(param);
                                        if (value) {
                                            authParams[param] = value;
                                        }
                                    });

                                    console.log('📋 授权参数:', JSON.stringify(authParams, null, 2));
                                    console.log('==========================================\n');

                                    // 保存重定向响应
                                    const redirectData = {
                                        timestamp: new Date().toISOString(),
                                        originalUrl: url,
                                        redirectUrl: location,
                                        authParams: authParams,
                                        status: status
                                    };

                                    const redirectFile = path.join(this.imageDir, 'authorization_redirect.json');
                                    fs.writeFileSync(redirectFile, JSON.stringify(redirectData, null, 2), 'utf8');
                                    this.log(`💾 重定向授权响应已保存到: authorization_redirect.json`);
                                } catch (e) {
                                    this.log(`解析重定向URL失败: ${e.message}`);
                                }
                            }
                        }
                    }

                    // 尝试获取响应体（如果是JSON）
                    try {
                        const contentType = response.headers()['content-type'] || '';
                        if (contentType.includes('application/json')) {
                            const responseBody = await response.text();
                            if (responseBody && (responseBody.includes('authorization_code') ||
                                responseBody.includes('access_token') || responseBody.includes('code'))) {
                                console.log('\n🔐 ===== 授权相关JSON响应 =====');
                                console.log('📍 URL:', url);
                                console.log('📋 响应体:', responseBody);
                                console.log('===============================\n');

                                const jsonResponseFile = path.join(this.imageDir, 'authorization_json_response.json');
                                const jsonData = {
                                    timestamp: new Date().toISOString(),
                                    url: url,
                                    status: status,
                                    responseBody: responseBody
                                };
                                fs.writeFileSync(jsonResponseFile, JSON.stringify(jsonData, null, 2), 'utf8');
                                this.log(`💾 JSON授权响应已保存到: authorization_json_response.json`);
                            }
                        }
                    } catch (e) {
                        // 忽略响应体读取错误
                    }
                }
            });

            this.log('✅ 网络监听设置完成');

        } catch (error) {
            this.log(`❌ 设置网络监听失败: ${error.message}`);
        }
    }

    // 捕获授权响应
    async captureAuthorizationResponse() {
        try {
            this.log('🔍 开始监听授权响应...');

            // 等待页面可能的跳转或响应
            let maxWaitTime = 30000; // 30秒超时
            let checkInterval = 1000; // 每秒检查一次
            let elapsed = 0;

            while (elapsed < maxWaitTime) {
                const currentUrl = this.page.url();
                this.log(`当前URL: ${currentUrl}`);

                // 检查URL是否包含授权响应参数
                if (currentUrl.includes('code=') || currentUrl.includes('access_token=') || currentUrl.includes('error=')) {
                    this.log('🎉 检测到授权响应！');
                    await this.takeScreenshot('authorization_response_detected');
                    await this.saveHtmlContent('authorization_response_detected');

                    // 解析URL参数
                    const urlParams = new URL(currentUrl);
                    const authResponse = {};

                    // 提取常见的OAuth参数
                    const params = ['code', 'access_token', 'token_type', 'expires_in', 'scope', 'state', 'error', 'error_description'];
                    params.forEach(param => {
                        const value = urlParams.searchParams.get(param);
                        if (value) {
                            authResponse[param] = value;
                        }
                    });

                    // 如果URL fragment中有参数（用于implicit flow）
                    if (urlParams.hash) {
                        const hashParams = new URLSearchParams(urlParams.hash.substring(1));
                        params.forEach(param => {
                            const value = hashParams.get(param);
                            if (value) {
                                authResponse[param] = value;
                            }
                        });
                    }

                    console.log('\n🔐 ===== 授权响应 =====');
                    console.log('📍 完整URL:', currentUrl);
                    console.log('📋 解析的参数:', JSON.stringify(authResponse, null, 2));
                    console.log('========================\n');

                    // 保存到文件
                    const responseData = {
                        timestamp: new Date().toISOString(),
                        fullUrl: currentUrl,
                        parsedParams: authResponse,
                        pageTitle: await this.page.title()
                    };

                    const responseFile = path.join(this.imageDir, 'authorization_response.json');
                    fs.writeFileSync(responseFile, JSON.stringify(responseData, null, 2), 'utf8');
                    this.log(`💾 授权响应已保存到: authorization_response.json`);

                    return authResponse;
                }

                // 检查页面内容是否包含授权相关信息
                const pageContent = await this.page.content();
                if (pageContent.includes('authorization_code') || pageContent.includes('access_token') ||
                    pageContent.includes('Authorization successful') || pageContent.includes('授权成功')) {
                    this.log('🎉 在页面内容中检测到授权信息！');
                    await this.takeScreenshot('authorization_content_detected');
                    await this.saveHtmlContent('authorization_content_detected');

                    // 尝试从页面中提取授权信息
                    const authInfo = await this.page.evaluate(() => {
                        const result = {};

                        // 查找包含授权码的元素
                        const codeElements = document.querySelectorAll('*');
                        for (let element of codeElements) {
                            const text = element.textContent || '';
                            if (text.includes('code:') || text.includes('authorization_code:')) {
                                result.pageContent = text;
                                break;
                            }
                        }

                        // 查找可能的JSON数据
                        const scripts = document.querySelectorAll('script');
                        for (let script of scripts) {
                            const content = script.textContent || '';
                            if (content.includes('authorization_code') || content.includes('access_token')) {
                                try {
                                    const match = content.match(/\{[^}]*(?:authorization_code|access_token)[^}]*\}/);
                                    if (match) {
                                        result.jsonData = JSON.parse(match[0]);
                                    }
                                } catch (e) {
                                    result.scriptContent = content;
                                }
                                break;
                            }
                        }

                        return result;
                    });

                    if (Object.keys(authInfo).length > 0) {
                        console.log('\n🔐 ===== 页面中的授权信息 =====');
                        console.log(JSON.stringify(authInfo, null, 2));
                        console.log('============================\n');

                        const responseFile = path.join(this.imageDir, 'authorization_page_content.json');
                        fs.writeFileSync(responseFile, JSON.stringify(authInfo, null, 2), 'utf8');
                        this.log(`💾 页面授权信息已保存到: authorization_page_content.json`);
                    }

                    return authInfo;
                }

                await this.wait(checkInterval);
                elapsed += checkInterval;
            }

            this.log('⏰ 授权响应监听超时，未检测到授权响应');
            return null;

        } catch (error) {
            this.log(`❌ 捕获授权响应时出错: ${error.message}`);
            return null;
        }
    }
}

async function main() {
    const autoRegister = new AutoRegister();

    try {
        console.log('🚀 Starting email verification process with One Mail API');

        // 使用你提供的URL，这里需要替换为实际的验证页面URL
        const verificationUrl = 'https://www.augmentcode.com/'; // 请替换为实际的邮箱验证页面URL

        await autoRegister.handleEmailVerificationWithOneMailAPI(verificationUrl);
        console.log('🎉 Email verification process completed successfully.');

    } catch (error) {
        console.error('💥 Process failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = AutoRegister;


<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1"><link rel="stylesheet" href="/assets/root-C-JJqdMW.css"><link rel="stylesheet" href="/assets/tailwind-DxnphuB3.css"><link rel="stylesheet" href="/assets/MaterialIcon-D6aQ-Xs1.css"><script type="text/javascript" crossorigin="anonymous" async="" src="https://us.i.posthog.com/static/array.js"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz" async="" status="loaded"></script><script type="text/javascript" src="https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.4/twitter-ads.dynamic.js.gz" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js" async="" status="loaded"></script><script type="text/javascript" async="" data-global-segment-analytics-key="analytics" src="https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js"></script><script type="text/javascript">(function() {
    var i = "analytics",
        analytics = window[i] = window[i] || [];
    if (!analytics.initialize) {
      if (analytics.invoked) {
        window.console && console.error && console.error("Segment snippet included twice.");
      } else {
        analytics.invoked = true;
        analytics.methods = [
          "trackSubmit", "trackClick", "trackLink", "trackForm", "pageview",
          "identify", "reset", "group", "track", "ready", "alias", "debug",
          "page", "screen", "once", "off", "on", "addSourceMiddleware",
          "addIntegrationMiddleware", "setAnonymousId", "addDestinationMiddleware",
          "register"
        ];
        analytics.factory = function(method) {
          return function() {
            if (window[i].initialized) {
              return window[i][method].apply(window[i], arguments);
            }
            var args = Array.prototype.slice.call(arguments);
            if (["track", "screen", "alias", "group", "page", "identify"].indexOf(method) > -1) {
              var canonicalLink = document.querySelector("link[rel='canonical']");
              args.push({
                __t: "bpc",
                c: (canonicalLink && canonicalLink.getAttribute("href")) || void 0,
                p: location.pathname,
                u: location.href,
                s: location.search,
                t: document.title,
                r: document.referrer
              });
            }
            args.unshift(method);
            analytics.push(args);
            return analytics;
          };
        };
        for (var n = 0; n < analytics.methods.length; n++) {
          var key = analytics.methods[n];
          analytics[key] = analytics.factory(key);
        }
        analytics.load = function(key, options) {
          var script = document.createElement("script");
          script.type = "text/javascript";
          script.async = true;
          script.setAttribute("data-global-segment-analytics-key", i);
          script.src = "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js";
          var firstScript = document.getElementsByTagName("script")[0];
          firstScript.parentNode.insertBefore(script, firstScript);
          analytics._loadOptions = options;
        };
        analytics._cdn = "https://evs.grdt.augmentcode.com";
        analytics._writeKey = "ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg";
        analytics.SNIPPET_VERSION = "5.2.0";
        analytics.load(analytics._writeKey);
        analytics.ready(() => {
          window.posthog.init(
            "phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW",
            {
              api_host: "https://us.i.posthog.com",
              segment: window.analytics,
              capture_pageview: false,
              capture_pageleave: true,
            }
          );
          if (window.analyticsInitialized) {
            window.analyticsInitialized.resolve();
          } else {
            console.error("analytics deferred promise not found");
          }
        });
      }
    }
  })();
  </script><script type="text/javascript">(function(document, posthog) {
    var methodList, methodIndex, scriptElement, firstScript;
    if (!posthog.__SV) {
      window.posthog = posthog;
      posthog._i = [];
      posthog.init = function(apiKey, config, namespace) {
        // Create a stub function that collects method calls until the real library loads.
        function createStub(target, methodName) {
          var parts = methodName.split(".");
          if (parts.length === 2) {
            target = target[parts[0]];
            methodName = parts[1];
          }
          target[methodName] = function() {
            target.push([methodName].concat(Array.prototype.slice.call(arguments, 0)));
          };
        }
        // Create and insert the script element to load the PostHog library.
        scriptElement = document.createElement("script");
        scriptElement.type = "text/javascript";
        scriptElement.crossOrigin = "anonymous";
        scriptElement.async = true;
        scriptElement.src = config.api_host + "/static/array.js";
        firstScript = document.getElementsByTagName("script")[0];
        firstScript.parentNode.insertBefore(scriptElement, firstScript);
        // Initialize the PostHog namespace.
        var ph = posthog;
        if (namespace !== undefined) {
          ph = posthog[namespace] = [];
        } else {
          namespace = "posthog";
        }
        ph.people = ph.people || [];
        ph.toString = function(stub) {
          var label = "posthog";
          if (namespace !== "posthog") {
            label += "." + namespace;
          }
          if (!stub) {
            label += " (stub)";
          }
          return label;
        };
        ph.people.toString = function() {
          return ph.toString(1) + ".people (stub)";
        };
        // List of methods to be stubbed until the library loads.
        methodList = "capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys getNextSurveyStep".split(" ");
        for (methodIndex = 0; methodIndex < methodList.length; methodIndex++) {
          createStub(ph, methodList[methodIndex]);
        }
        // Store initialization arguments for later use.
        posthog._i.push([apiKey, config, namespace]);
      };
      posthog.__SV = 1;
    }
  })(document, window.posthog || []);
  </script><script>window.FEATURE_FLAGS = {
  "auth_central_user_tier_change": true,
  "team_management": true,
  "team_management_canary_domains": "augm.io,turing.com",
  "customer_ui_enable_user_feature_stats": true,
  "customer_ui_content_deletion_enabled": true,
  "block_trial_to_community_conversions": true,
  "block_paid_to_community_conversions": true
}</script></head><body><div data-is-root-theme="true" data-accent-color="indigo" data-gray-color="slate" data-has-background="true" data-panel-background="translucent" data-radius="medium" data-scaling="100%" class="radix-themes"><div class="rt-Container rt-r-size-4 rt-r-mx-4"><div class="rt-ContainerInner"><style scoped="">@scope {
  :scope .layout-container {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding: 0 var(--ds-spacing-4);
    margin-top: 0;
    padding-top: 0;
  }
  
  @media (min-width: 768px) {
    :scope .layout-container {
      padding: 0 var(--ds-spacing-6);
    }
  }
}</style><div data-testid="topnav" class="rt-Box topnav-container"><style scoped="">@scope {
  /* Scoped styles for TopNav component */
            .tab-link {
              font-weight: 600 !important;
              font-size: 15px !important;
              padding: 8px 12px !important;
              letter-spacing: 0 !important;
              text-rendering: optimizeLegibility !important;
              -webkit-font-smoothing: antialiased !important;
              font-feature-settings: "kern" !important;
            }
  
            .tab-link-active {
              color: var(--slate-11) !important;
              opacity: 1 !important;
            }
  
            .tab-link-inactive {
              opacity: 0.7 !important;
            }
  
            /* Target inner elements as well */
            .tab-link a,
            .tab-link span,
            .tab-link div {
              font-weight: 600 !important;
              font-size: 15px !important;
              letter-spacing: 0 !important;
            }
  
            /* Tab navigation animation with CSSTransition */
            :scope .tab-nav-container {
              overflow: hidden;
              margin-top: 12px;
            }
  
            /* Use CSSTransition animation functions */
            .tab-nav-enter {
    opacity: 0;
  }
  
  .tab-nav-enter-active {
    opacity: 1;
  }
  
  .tab-nav-exit {
    opacity: 1;
  }
  
  .tab-nav-exit-active {
    opacity: 0;
  }
            --transform-enter-2: translateY(-10px);
  --transform-enter-active-2: translateY(0px);
  --transform-exit-2: translateY(0px);
  --transform-exit-active-2: translateY(-10px);
  
            /* Custom height animation for tab navigation */
            :scope .tab-nav-enter {
              max-height: 0;
            }
  
            :scope .tab-nav-enter-active {
              max-height: 60px;
            }
  
            :scope .tab-nav-exit {
              max-height: 60px;
            }
  
            :scope .tab-nav-exit-active {
              max-height: 0;
            }
  
            /* Plan section styles */
            :scope .topnav-plan-section {
              position: relative;
              z-index: 1;
            }
  
            :scope .topnav-plan-section.hidden {
              opacity: 0;
            }
  
            :scope .topnav-plan-text-real {
              font-size: 15px;
              letter-spacing: -0.02em;
            }
  
            /* Plan text gradient styles */
            :scope .topnav-plan-text-gradient {
              background: linear-gradient(90deg, var(--plan-color), color-mix(in srgb, var(--plan-color) 70%, #000));
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              color: inherit;
            }
  
            :scope .topnav-plan-text-pending {
              background: none;
              -webkit-background-clip: unset;
              -webkit-text-fill-color: var(--plan-color);
              color: var(--plan-color);
            }
  
            /* TopNav container - sticky positioning for entire header + nav */
            :scope.topnav-container {
              position: sticky;
              top: 0;
              z-index: 100;
              backdrop-filter: blur(10px);
              -webkit-backdrop-filter: blur(10px);
              background: rgba(255, 255, 255, 0.95);
              padding-bottom: 18px;
              margin-bottom: 12px;
            }
  
            :scope .topnav-content {
              margin: 0 auto;
              width: 100%;
              padding: 0 var(--ds-spacing-4);
              /* Ensure consistent layout during loading */
              min-height: 38px;
  
              @media (min-width: 768px) {
                padding: 0 var(--ds-spacing-6);
              }
            }
  
            /* Tab navigation styles */
            :scope .topnav-tabs {
              border-bottom: none;
              position: relative;
              z-index: 2;
              margin-top: 6px;
            }
}</style><div class="rt-Box base-header-container "><style scoped="">@scope {
  /* Scoped styles for BaseHeader component */
  :scope.base-header-container {
    border-radius: 0;
    overflow: hidden;
    padding: 18px 0;
    width: 100%;
    /* Prevent layout shift during hydration */
    contain: layout style;
    
  }
  
  :scope .base-header-content {
    margin: 0 auto;
    width: 100%;
    padding: 0 32px;
    /* Ensure consistent layout during loading */
    min-height: 38px;
  }
  
  :scope .base-header-logo-section {
    position: relative;
  }
  
  :scope .base-header-logo {
    width: 140px;
    height: 38px;
  }
  
  :scope .base-header-logo-link {
    display: inline-block;
    transition: opacity 0.2s ease;
    cursor: pointer;
  }
  
  :scope .base-header-logo-link:hover {
    opacity: 0.8;
  }
  
  /* Email styles */
  :scope .base-header-email {
    opacity: 0.9;
    font-weight: 500;
    font-size: 15px;
  }
  
  /* Logout button styles */
  :scope .base-header-logout-button {
    transition: all 0.2s ease;
    font-weight: 600;
    font-size: 15px;
    opacity: 0.9;
    padding: 8px 16px;
  }
  
  /* Right section container to prevent layout shift */
  :scope .base-header-right-section {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 200px; /* Prevent collapse during loading */
    justify-content: flex-end;
  }
  
  /* Logo section styles */
  :scope .base-header-logo-section {
    position: relative;
  }
}</style><div class="rt-Box base-header-content" style="max-width: 1200px;"><div class="rt-Flex rt-r-fd-column sm:rt-r-fd-row rt-r-ai-start sm:rt-r-ai-center rt-r-jc-space-between rt-r-gap-3 sm:rt-r-gap-0"><div class="rt-Flex rt-r-ai-center rt-r-gap-4 rt-r-py-1 base-header-logo-section"><img src="/augment-logo.svg" alt="Augment Logo" class="base-header-logo"><div class="rt-Flex rt-r-ai-center rt-r-gap-3"><div class="rt-Flex rt-r-ai-center rt-r-gap-1 topnav-plan-section "><svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" style="color: var(--gray-9);"><path d="M7.50009 0.877014C3.84241 0.877014 0.877258 3.84216 0.877258 7.49984C0.877258 11.1575 3.8424 14.1227 7.50009 14.1227C11.1578 14.1227 14.1229 11.1575 14.1229 7.49984C14.1229 3.84216 11.1577 0.877014 7.50009 0.877014ZM1.82726 7.49984C1.82726 4.36683 4.36708 1.82701 7.50009 1.82701C10.6331 1.82701 13.1729 4.36683 13.1729 7.49984C13.1729 10.6328 10.6331 13.1727 7.50009 13.1727C4.36708 13.1727 1.82726 10.6328 1.82726 7.49984ZM8 4.50001C8 4.22387 7.77614 4.00001 7.5 4.00001C7.22386 4.00001 7 4.22387 7 4.50001V7.50001C7 7.63262 7.05268 7.7598 7.14645 7.85357L9.14645 9.85357C9.34171 10.0488 9.65829 10.0488 9.85355 9.85357C10.0488 9.65831 10.0488 9.34172 9.85355 9.14646L8 7.29291V4.50001Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg><span class="rt-Text rt-r-size-2 rt-r-weight-bold topnav-plan-text-real topnav-plan-text-gradient" style="--plan-color: var(--gray-9);">Trial Plan</span></div></div></div><div class="rt-Flex rt-r-ai-center rt-r-gap-3 base-header-right-section"><span data-accent-color="gray" data-testid="user-email" class="rt-Text rt-r-size-2 base-header-email"><EMAIL></span><div class="rt-Box"><form method="get" action="/logout" data-discover="true"><button data-accent-color="gray" type="submit" data-testid="logout-button" class="rt-reset rt-BaseButton rt-r-size-2 rt-variant-soft rt-Button base-header-logout-button">Logout</button></form></div></div></div></div></div><div class="rt-Box topnav-content" style="max-width: 1200px;"><div class="rt-Flex rt-r-fd-column rt-r-gap-0"><div class="rt-Box tab-nav-container tab-nav-enter-done"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="rt-TabNavRoot"><div style="position: relative;"><ul data-orientation="horizontal" class="rt-reset rt-BaseTabList rt-TabNavList rt-r-size-2 topnav-tabs" dir="ltr"><li class="rt-TabNavItem"><a data-active="" data-testid="tab-link-subscription" data-radix-collection-item="" data-discover="true" aria-current="page" class="rt-reset rt-BaseTabListTrigger rt-TabNavLink tab-link tab-link-active active" href="/account/subscription"><span class="rt-BaseTabListTriggerInner rt-TabNavLinkInner">Subscription</span><span class="rt-BaseTabListTriggerInnerHidden rt-TabNavLinkInnerHidden">Subscription</span></a></li><li class="rt-TabNavItem"><a data-testid="tab-link-team" data-radix-collection-item="" data-discover="true" class="rt-reset rt-BaseTabListTrigger rt-TabNavLink tab-link tab-link-inactive" href="/account/team"><span class="rt-BaseTabListTriggerInner rt-TabNavLinkInner">Team</span><span class="rt-BaseTabListTriggerInnerHidden rt-TabNavLinkInnerHidden">Team</span></a></li></ul></div></nav></div><style scoped="">@scope {
  :scope {
            --transition-property-enter: "all";
            --transition-property-exit: "all";
            --transition-property-appear: "all";
            --transition-timing-function-enter: "ease";
            --transition-timing-function-exit: "ease";
            --transition-timing-function-appear: "ease";
            --transition-enter-duration: 300ms;
            --transition-exit-duration: 300ms;
            --transition-appear-duration: 300ms;
  
            /* CSS Variables for transforms */
  --transform-noop: matrix(1, 0, 0, 1, 0, 0);
  --transform-enter: 
    var(--transform-enter-1, var(--transform-noop))
    var(--transform-enter-2, var(--transform-noop));
  
  .tab-nav-enter {
          transform: var(--transform-enter);
        }
  
  --transform-enter-active: 
    var(--transform-enter-active-1, var(--transform-noop))
    var(--transform-enter-active-2, var(--transform-noop));
  
  .tab-nav-enter-active {
          transform: var(--transform-enter-active);
        }
  
  --transform-exit: 
    var(--transform-exit-1, var(--transform-noop))
    var(--transform-exit-2, var(--transform-noop));
  
  .tab-nav-exit {
          transform: var(--transform-exit);
        }
  
  --transform-exit-active: 
    var(--transform-exit-active-1, var(--transform-noop))
    var(--transform-exit-active-2, var(--transform-noop));
  
  .tab-nav-exit-active {
          transform: var(--transform-exit-active);
        }
  
  
  
            .tab-nav-enter-active {
              transition-property: var(--transition-property-enter);
              transition-timing-function: var(--transition-timing-function-enter);
              transition-duration: var(--transition-enter-duration);
            }
  
            .tab-nav-exit-active {
              transition-property: var(--transition-property-exit);
              transition-timing-function: var(--transition-timing-function-exit);
              transition-duration: var(--transition-exit-duration);
            }
  
            .tab-nav-appear-active {
              transition-property: var(--transition-property-appear);
              transition-timing-function: var(
                --transition-timing-function-appear
              );
              transition-duration: var(--transition-appear-duration);
            }
          }
}</style></div></div></div><div class="rt-Box layout-container"><div class="account-layout"><div class="rt-Box rt-r-mb-8" style="width: 100%;"><style scoped="">@scope {
  :scope {
    .subtle-text {
      color: var(--ds-color-neutral-11);
      font-size: var(--ds-font-size-2);
    }
    .pending-credits {
      color: var(--ds-color-neutral-11);
      font-size: var(--ds-font-size-2);
      margin-left: var(--ds-spacing-3);
      font-style: italic;
    }
    .change-subscription-card {
      margin-bottom: 32px;
    }
    .change-subscription-card--scheduled {
      background-color: var(--ds-color-warning-2);
      border: 1px solid var(--ds-color-warning-6);
    }
    .warning-text {
      color: var(--ds-text-warning);
    }
    .warning-button {
      background-color: var(--ds-color-warning-4);
      color: var(--ds-color-warning-11);
      border: 1px solid var(--ds-color-warning-6);
    }
    .warning-button:hover {
      background-color: var(--ds-color-warning-5);
    }
    .unschedule-button {
      background-color: var(--ds-color-neutral-2);
      color: var(--ds-color-neutral-11);
      border: 1px solid var(--ds-color-neutral-6);
    }
    .unschedule-button:hover {
      background-color: var(--ds-color-neutral-3);
    }
  }
}</style><div class="rt-reset rt-BaseCard rt-Card rt-r-size-1 rt-variant-surface bg-gradient-to-r from-amber-500/90 to-orange-500" style="margin-bottom: 24px;"><div class="rt-Flex rt-r-ai-center rt-r-jc-space-between rt-r-gap-4"><div class="rt-Flex rt-r-ai-center rt-r-gap-2"><svg width="17" height="17" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.22303 0.665992C7.32551 0.419604 7.67454 0.419604 7.77702 0.665992L9.41343 4.60039C9.45663 4.70426 9.55432 4.77523 9.66645 4.78422L13.914 5.12475C14.18 5.14607 14.2878 5.47802 14.0852 5.65162L10.849 8.42374C10.7636 8.49692 10.7263 8.61176 10.7524 8.72118L11.7411 12.866C11.803 13.1256 11.5206 13.3308 11.2929 13.1917L7.6564 10.9705C7.5604 10.9119 7.43965 10.9119 7.34365 10.9705L3.70718 13.1917C3.47945 13.3308 3.19708 13.1256 3.25899 12.866L4.24769 8.72118C4.2738 8.61176 4.23648 8.49692 4.15105 8.42374L0.914889 5.65162C0.712228 5.47802 0.820086 5.14607 1.08608 5.12475L5.3336 4.78422C5.44573 4.77523 5.54342 4.70426 5.58662 4.60039L7.22303 0.665992Z" fill="orange"></path></svg><span class="rt-Text">Your trial ends in in 7 days</span></div><button data-accent-color="orange" class="rt-reset rt-BaseButton rt-r-size-2 rt-variant-solid rt-Button">Upgrade plan</button></div></div><h1 data-testid="subscription-heading" class="rt-Heading rt-r-size-8" style="margin-bottom: 16px;">Subscription</h1><div class="rt-Text rt-r-size-3 subtle-text" style="margin-bottom: 24px;">Manage your subscription and billing details.</div><div class="rt-Flex rt-r-fd-column sm:rt-r-fd-row rt-r-gap-4" style="margin-bottom: 32px;"><div class="rt-reset rt-BaseCard rt-Card rt-r-size-3 rt-variant-surface" style="flex: 1 1 0%;"><style scoped="">@scope {
  :scope {
    flex: 1;
  
    .c-credits-card__flex-wrapper {
      height: 100%;
    }
  
    .c-credits-card__upgrade-button {
      width: 100%;
    }
  }
}</style><div class="rt-Flex rt-r-fd-column rt-r-gap-3 c-credits-card__flex-wrapper"><div class="rt-Flex rt-r-ai-center rt-r-jc-space-between"><span class="rt-Text rt-r-weight-medium">User Messages</span><a data-accent-color="" href="https://portal.withorb.com/view?token=IjdlNTlycHFBd2hoTkxneEMi.5aC4CwUAVpIbGcDMH4kHYyD74-I" target="_blank" class="rt-Text rt-reset rt-Link rt-r-size-2 rt-underline-auto">View usage</a></div><div class="rt-Flex rt-r-ai-start rt-r-gap-2"><div class="rounded-full p-2 bg-primary/10"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-coins h-5 w-5 text-primary"><circle cx="8" cy="8" r="6"></circle><path d="M18.09 10.37A6 6 0 1 1 10.34 18"></path><path d="M7 6h1v4"></path><path d="m16.71 13.88.7.71-2.82 2.82"></path></svg></div><div class="rt-Flex rt-r-fd-column rt-r-gap-1"><span class="rt-Text rt-r-size-5 rt-r-weight-medium"><span aria-hidden="true" class="rt-Skeleton" data-inline-skeleton="true" tabindex="-1" inert="">0 available</span></span></div></div><div><div aria-valuemax="1" aria-valuemin="0" aria-valuenow="1" aria-valuetext="100%" role="progressbar" data-state="complete" data-value="1" data-max="1" class="rt-ProgressRoot rt-r-size-2 rt-variant-surface rt-Skeleton rt-r-w" aria-hidden="true" tabindex="-1" inert="" style="--progress-value: 1; --progress-max: 1; --width: 100%;"><div data-state="complete" data-value="1" data-max="1" class="rt-ProgressIndicator"></div></div></div><span class="rt-Text subtle-text"><span aria-hidden="true" class="rt-Skeleton" data-inline-skeleton="true" tabindex="-1" inert="">Used 1 of 1 this month</span></span><div class="rt-Flex rt-r-ai-end rt-r-fg-1"><button data-accent-color="" class="rt-reset rt-BaseButton rt-r-size-2 rt-variant-soft rt-Button c-credits-card__upgrade-button">Upgrade plan to add user messages</button></div></div></div><div class="rt-reset rt-BaseCard rt-Card rt-r-size-3 rt-variant-surface"><style scoped="">@scope {
  :scope {
    flex: 1;
    display: flex;
    flex-direction: column;
  
    .c-billing-card__flex-wrapper {
      height: 100%;
    }
  
    .c-billing-card__button-container {
      width: 100%;
    }
  
    .c-billing-card__button {
      flex-grow: 1;
    }
  
    .subtle-text {
      color: var(--ds-color-neutral-11);
      font-size: var(--ds-font-size-2);
    }
  
    .c-billing-card__payment-error {
      margin-top: var(--ds-spacing-2);
    }
  }
}</style><div class="rt-Flex rt-r-fd-column rt-r-gap-3 c-billing-card__flex-wrapper"><div class="rt-Flex rt-r-ai-center rt-r-jc-space-between"><span class="rt-Text rt-r-weight-medium">Billing</span><a data-accent-color="" href="https://portal.withorb.com/view?token=IjdlNTlycHFBd2hoTkxneEMi.5aC4CwUAVpIbGcDMH4kHYyD74-I" target="_blank" class="rt-Text rt-reset rt-Link rt-r-size-2 rt-underline-auto">Payment history</a></div><div class="rt-Flex rt-r-ai-start rt-r-gap-2"><div class="rounded-full p-2 bg-primary/10"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-5 w-5 text-primary"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg></div><div class="rt-Flex rt-r-fd-column rt-r-gap-1"><span class="rt-Text rt-r-size-5 rt-r-weight-medium">August 21, 2025</span><span class="rt-Text subtle-text">Trial End Date</span></div></div><div class="subtle-text">No payment method on file</div><div class="rt-Flex rt-r-ai-end rt-r-fg-1"><div class="rt-Flex rt-r-fd-column rt-r-gap-3 c-billing-card__button-container"><button data-accent-color="blue" class="rt-reset rt-BaseButton rt-r-size-2 rt-variant-solid rt-Button">Upgrade Now</button></div></div></div></div></div><h1 data-testid="current-plan-heading" class="rt-Heading rt-r-size-5" style="margin-bottom: 16px;">Current plan</h1><div class="rt-reset rt-BaseCard rt-Card rt-r-size-3 rt-variant-surface" style="margin-bottom: 32px;"><div class="rt-Flex rt-r-fd-column rt-r-gap-3"><div class="rt-Flex rt-r-ai-start rt-r-gap-2"><span data-testid="current-plan-name" class="rt-Text rt-r-size-4 rt-r-weight-medium">Trial Plan</span><div class="rt-Box" style="background-color: var(--ds-color-warning-4); padding: 2px 8px; border-radius: 10px; font-size: 12px;">Trial ends in in 7 days</div><div class="rt-Flex rt-r-fg-1"></div></div><div class="rt-Flex rt-r-fd-column rt-r-gap-2"><div class="rt-Flex rt-r-ai-center rt-r-gap-2"><svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" style="color: rgb(76, 175, 80);"><path d="M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg><span data-accent-color="gray" class="rt-Text rt-r-size-2" style="font-style: normal; opacity: 1;">50 user messages </span></div><div class="rt-Flex rt-r-ai-center rt-r-gap-2"><svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" style="color: rgb(76, 175, 80);"><path d="M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg><span data-accent-color="gray" class="rt-Text rt-r-size-2" style="font-style: normal; opacity: 1;">Context Engine</span></div><div class="rt-Flex rt-r-ai-center rt-r-gap-2"><svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" style="color: rgb(76, 175, 80);"><path d="M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg><span data-accent-color="gray" class="rt-Text rt-r-size-2" style="font-style: normal; opacity: 1;">MCP &amp; Native Tools</span></div><a data-accent-color="" href="#" class="rt-Text rt-reset rt-Link rt-r-size-2 rt-underline-auto" style="text-decoration: none; margin-left: 24px;">Show more</a></div><div class="rt-Flex rt-r-ai-center rt-r-gap-2" style="margin-top: 8px;"><span class="rt-Text subtle-text">$0.00/user/mo</span><span class="rt-Text subtle-text">•</span><span class="rt-Text subtle-text">1 seat purchased</span></div><span class="rt-Text rt-r-size-2 rt-r-weight-medium" style="margin-top: 8px;">Monthly total: $0.00</span></div></div><div class="rt-reset rt-BaseCard rt-Card rt-r-size-3 rt-variant-surface change-subscription-card"><div class="rt-Flex rt-r-fd-column sm:rt-r-fd-row rt-r-jc-space-between rt-r-gap-4"><div class="rt-Flex rt-r-fd-column rt-r-gap-1"><span class="rt-Text rt-r-weight-medium">Change your subscription</span><span class="rt-Text subtle-text">Switch plans or contact sales about Enterprise options</span></div><div class="rt-Flex rt-r-fd-column rt-r-gap-2 rt-r-w sm:rt-r-w" style="--width-sm: auto; --width: 100%;"><button data-accent-color="" data-testid="change-plan-button" class="rt-reset rt-BaseButton rt-r-size-2 rt-variant-soft rt-Button">Change plan</button></div></div></div><h1 class="rt-Heading rt-r-size-5" style="margin-bottom: 16px;">Danger zone</h1><div class="rt-reset rt-BaseCard rt-Card rt-r-size-3 rt-variant-surface"><div class="rt-Flex rt-r-fd-column rt-r-jc-center rt-r-gap-0"><div class="rt-Flex rt-r-fd-row data-deletion-outer-flex"><div class="rt-Flex rt-r-fd-column"><span data-accent-color="red" class="rt-Text rt-r-weight-medium">Delete Indexed Code</span><span data-accent-color="gray" class="rt-Text rt-r-size-2">Permanently delete your indexed code</span></div><div class="rt-Flex rt-r-fd-column rt-r-jc-center"><button data-accent-color="red" class="rt-reset rt-BaseButton rt-r-size-2 rt-variant-soft rt-Button">Delete Indexed Code</button></div></div><div class="rt-Flex"></div></div></div><style scoped="">@scope {
  .data-deletion-outer-flex {
    justify-content: space-between;
  }
  
  .delete-code-button {
    background-color: var(--ds-color-error-3);
    color: var(--ds-color-error-11);
    border: 1px solid var(--ds-color-error-6);
    transition: all 0.2s ease;
  }
  .delete-code-button:hover {
    background-color: var(--ds-color-error-4);
    border: 1px solid var(--ds-color-error-7);
  }
}</style></div><style scoped="">@scope {
  :scope.account-layout {
    display: flex;
    flex-direction: row;
    gap: 24px;
  }
  :scope.select-plan-page {
    display: block;
  }
}</style></div></div></div></div><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events:none"><ol tabindex="-1" class="ToastViewport"></ol></div></div><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/web-vitals.js?v=1.260.0"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/surveys.js?v=1.260.0"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/dead-clicks-autocapture.js?v=1.260.0"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/recorder.js?v=1.260.0"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/array/phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW/config.js"></script><script>((STORAGE_KEY, restoreKey) => {
    if (!window.history.state || !window.history.state.key) {
      let key = Math.random().toString(32).slice(2);
      window.history.replaceState({
        key
      }, "");
    }
    try {
      let positions = JSON.parse(sessionStorage.getItem(STORAGE_KEY) || "{}");
      let storedY = positions[restoreKey || window.history.state.key];
      if (typeof storedY === "number") {
        window.scrollTo(0, storedY);
      }
    } catch (error) {
      console.error(error);
      sessionStorage.removeItem(STORAGE_KEY);
    }
  })("positions", null)</script><link rel="modulepreload" href="/assets/manifest-46feb8ab.js"><link rel="modulepreload" href="/assets/entry.client-C92V1BwZ.js"><link rel="modulepreload" href="/assets/index-Bi4s4-Io.js"><link rel="modulepreload" href="/assets/index-BS38kjqr.js"><link rel="modulepreload" href="/assets/index-B7Ui2t93.js"><link rel="modulepreload" href="/assets/components--HLsvfrm.js"><link rel="modulepreload" href="/assets/QueryClientProvider-CeGnmbe-.js"><link rel="modulepreload" href="/assets/queryClient.client-Dk3lS3wN.js"><link rel="modulepreload" href="/assets/client-only-C74SDDMq.js"><link rel="modulepreload" href="/assets/index.modern-950P1XoK.js"><link rel="modulepreload" href="/assets/theme-C1ulz75E.js"><link rel="modulepreload" href="/assets/container-BlJCmUTg.js"><link rel="modulepreload" href="/assets/card-BBgKeY7L.js"><link rel="modulepreload" href="/assets/link-CMt6MnuB.js"><link rel="modulepreload" href="/assets/flex-C9XhsxSj.js"><link rel="modulepreload" href="/assets/button-Dvrjyl3p.js"><link rel="modulepreload" href="/assets/Toast-CG_NC-6_.js"><link rel="modulepreload" href="/assets/index-DrFu-skq.js"><link rel="modulepreload" href="/assets/jotaiStore.client-sdvKmlSn.js"><link rel="modulepreload" href="/assets/react-C8U1uDHl.js"><link rel="modulepreload" href="/assets/constants-BkoMXhYo.js"><link rel="modulepreload" href="/assets/index-CI4icoal.js"><link rel="modulepreload" href="/assets/index-CAuWbg93.js"><link rel="modulepreload" href="/assets/spinner-Cq6egsy4.js"><link rel="modulepreload" href="/assets/index-CAyM6kBC.js"><link rel="modulepreload" href="/assets/get-subtree-8AxxbxX_.js"><link rel="modulepreload" href="/assets/base-button-Dk95TXPu.js"><link rel="modulepreload" href="/assets/index-B5mzPb5P.js"><link rel="modulepreload" href="/assets/react-icons.esm-g3l3pVh3.js"><link rel="modulepreload" href="/assets/root-Cup3efAs.js"><link rel="modulepreload" href="/assets/useQuery-BvSAfNQo.js"><link rel="modulepreload" href="/assets/animations-CMbVnQEg.js"><link rel="modulepreload" href="/assets/style-Bv9a6v44.js"><link rel="modulepreload" href="/assets/BaseHeader-y1wz0aO3.js"><link rel="modulepreload" href="/assets/string-CwzBSc0v.js"><link rel="modulepreload" href="/assets/user-d_3utlAo.js"><link rel="modulepreload" href="/assets/guards-C20ItfmI.js"><link rel="modulepreload" href="/assets/plans-D8s3V0en.js"><link rel="modulepreload" href="/assets/subscription-creation-pending-Mylp5-_d.js"><link rel="modulepreload" href="/assets/skeleton-qwMe81ym.js"><link rel="modulepreload" href="/assets/feature-flags.client-BVZhVN7G.js"><link rel="modulepreload" href="/assets/box-DvlTT8Qh.js"><link rel="modulepreload" href="/assets/index-D-DXDI2l.js"><link rel="modulepreload" href="/assets/queryOptions-Yjo86aMs.js"><link rel="modulepreload" href="/assets/toDate-qOSwr3PX.js"><link rel="modulepreload" href="/assets/addLeadingZeros-6--iqVZy.js"><link rel="modulepreload" href="/assets/proto3-Bmo7MjaP.js"><link rel="modulepreload" href="/assets/_layout-s5dvxSMq.js"><link rel="modulepreload" href="/assets/ProgressPage-CfNQ5HtT.js"><link rel="modulepreload" href="/assets/heading-Duq80h8F.js"><link rel="modulepreload" href="/assets/_layout.account--DlvNh9L.js"><link rel="modulepreload" href="/assets/MaterialIcon-BneX_s9R.js"><link rel="modulepreload" href="/assets/PlanPicker-CZi5-vIO.js"><link rel="modulepreload" href="/assets/Card-BR5rB2rc.js"><link rel="modulepreload" href="/assets/number-BS8GKe3y.js"><link rel="modulepreload" href="/assets/icons--M48DCb3.js"><link rel="modulepreload" href="/assets/plural-D9YAiM4O.js"><link rel="modulepreload" href="/assets/Enabled-BoZ8Au2f.js"><link rel="modulepreload" href="/assets/index-BEyGE8AK.js"><link rel="modulepreload" href="/assets/constants-C5gnWpVx.js"><link rel="modulepreload" href="/assets/url-_DgIuZOw.js"><link rel="modulepreload" href="/assets/Badge-CCBfROU-.js"><link rel="modulepreload" href="/assets/isBefore-DuJnhAXP.js"><link rel="modulepreload" href="/assets/badge-CrInsKkE.js"><link rel="modulepreload" href="/assets/index-DzvzAwJl.js"><link rel="modulepreload" href="/assets/constructFrom-DWjd9ymD.js"><link rel="modulepreload" href="/assets/_layout.account.subscription-BBRL8heg.js"><script>window.__remixContext = {"basename":"/","future":{"v3_fetcherPersist":true,"v3_relativeSplatPath":true,"v3_throwAbortReason":true,"v3_routeConfig":false,"v3_singleFetch":false,"v3_lazyRouteDiscovery":false,"unstable_optimizeDeps":false},"isSpaMode":false,"state":{"loaderData":{"root":{"earliestData":{"year":2025,"month":2,"day":25},"featureFlags":{"auth_central_user_tier_change":true,"team_management":true,"team_management_canary_domains":"augm.io,turing.com","customer_ui_enable_user_feature_stats":true,"customer_ui_content_deletion_enabled":true,"block_trial_to_community_conversions":true,"block_paid_to_community_conversions":true},"user":{"userId":"ee119a0e-84b8-4df9-bacd-336202ac30b8","tenantId":"25b6ad8d93f971ba1cb50c14bf2fcaa3","tenantName":"d20-discovery1","shardNamespace":"d20","email":"<EMAIL>","roles":[],"createdAt":*************,"sessionId":"18a7a185-752a-4c67-a8e2-2a307b1037ee"}},"routes/_layout.account":{},"routes/_layout.account.subscription":{"userId":"ee119a0e-84b8-4df9-bacd-336202ac30b8","suspensions":[]},"routes/_layout":null},"actionData":null,"errors":null}};</script><iframe height="0" width="0" style="display: none; visibility: hidden;"></iframe><script type="module" async="">import "/assets/manifest-46feb8ab.js";
import * as route0 from "/assets/root-Cup3efAs.js";
import * as route1 from "/assets/_layout-s5dvxSMq.js";
import * as route2 from "/assets/_layout.account--DlvNh9L.js";
import * as route3 from "/assets/_layout.account.subscription-BBRL8heg.js";

window.__remixRouteModules = {"root":route0,"routes/_layout":route1,"routes/_layout.account":route2,"routes/_layout.account.subscription":route3};

import("/assets/entry.client-C92V1BwZ.js");</script></body></html>
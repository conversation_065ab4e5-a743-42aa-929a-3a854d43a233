{"initialStatus": {"isResolved": false, "turnstileStatus": null, "recaptchaStatus": null, "visibleElements": [{"type": "iframe", "index": 0, "visible": false, "src": "no-src", "dimensions": {"width": 0, "height": 0}}], "hiddenInputs": []}, "yescaptchaUsed": true, "token": null, "injectionResult": null, "finalStatus": {"isResolved": true, "turnstileStatus": "resolved", "recaptchaStatus": null, "visibleElements": [{"type": "iframe", "index": 0, "visible": false, "src": "no-src", "dimensions": {"width": 0, "height": 0}}], "hiddenInputs": [{"type": "turnstile-response", "index": 0, "hasValue": true, "valueLength": 1072}]}, "screenshots": ["yescaptcha_success.png", "after_token_injection.png"], "timestamp": "2025-08-14T10:39:56.661Z"}